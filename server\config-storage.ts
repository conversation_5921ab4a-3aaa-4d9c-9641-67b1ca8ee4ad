// Email provider interfaces
interface EmailCredentials {
  fromEmail: string;
  fromName: string;
  host: string;
  port: string;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

interface EmailProvider {
  id: string;
  name: string;
  active: boolean;
  isDefault?: boolean;
  isBackup?: boolean;
  credentials: EmailCredentials;
}

interface EmailConfig {
  providers: EmailProvider[];
}

// Payment provider interfaces
interface PayPalConfig {
  clientId: string;
  clientSecret: string;
  mode: string;
  webhookId: string;
  paypalEmail?: string; // Email associated with the PayPal account
}

interface CustomLinkItem {
  id: string;
  name: string;
  paymentLink: string; // The custom payment link to send to customers
  buttonText: string; // Text to display on the payment button
  successRedirectUrl?: string; // URL to redirect after successful payment
  active: boolean;
}

interface CustomLinkConfig {
  links: CustomLinkItem[]; // Array of custom payment links
  rotationMethod: 'round-robin' | 'random'; // Method to rotate between links
  lastUsedIndex?: number; // Index of the last used link (for round-robin)
}

interface PayPalButtonItem {
  id: string;
  name: string;
  buttonHtml: string; // The HTML code for the PayPal button
  description?: string; // Optional description of the button
  active: boolean;
}

interface PayPalButtonEmbedConfig {
  buttons: PayPalButtonItem[]; // Array of PayPal button embeds
  rotationMethod: 'round-robin' | 'random'; // Method to rotate between buttons
  lastUsedIndex?: number; // Index of the last used button (for round-robin)
}

// Union type for different payment provider configs
type PaymentProviderConfig = PayPalConfig | CustomLinkConfig | PayPalButtonEmbedConfig;

interface PaymentProvider {
  id: string;
  name: string;
  active: boolean;
  config: PaymentProviderConfig;
}

interface PaymentConfig {
  providers: PaymentProvider[];
}

// In-memory configuration storage for demo
export const configStorage = {
  email: {
    providers: [
      {
        id: 'smtp-1',
        name: 'Primary SMTP',
        active: true,
        isDefault: true,
        isBackup: false,
        credentials: {
          host: 'smtp-relay.brevo.com',
          port: '587',
          secure: false,
          auth: {
            user: '<EMAIL>',
            pass: '3d8I9xFm1yMDYj7W'
          },
          fromEmail: '<EMAIL>',
          fromName: 'PayPal Invoicer'
        }
      }
    ]
  },
  payment: {
    providers: [
      {
        id: 'paypal',
        name: 'PayPal',
        active: true,
        config: {
          clientId: '********************************************************************************',
          clientSecret: '********************************************************************************',
          mode: 'sandbox',
          webhookId: '',
          paypalEmail: '<EMAIL>' // Email associated with your PayPal account
        } as PayPalConfig
      },
      {
        id: 'custom-link',
        name: 'Custom Payment Links',
        active: true,
        config: {
          links: [
            {
              id: 'link-1',
              name: 'Default Payment Required',
              paymentLink: 'https://example.com/pay',
              buttonText: 'Complete Payment',
              successRedirectUrl: 'https://example.com/thank-you',
              active: true
            }
          ],
          rotationMethod: 'round-robin',
          lastUsedIndex: 0
        } as CustomLinkConfig
      },
      {
        id: 'paypal-button-embed',
        name: 'PayPal Button Embed',
        active: true,
        config: {
          buttons: [
            {
              id: 'button-1',
              name: 'Default PayPal Button',
              buttonHtml: `
<div style="text-align: center; margin: 20px 0;">
  <div style="max-width: 500px; margin: 0 auto; border: 1px solid #e0e0e0; border-radius: 5px; padding: 15px; background-color: #fafafa;">
    <div style="font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #2c2e2f;">
      Pay for {PRODUCT_NAME}
    </div>
    <div style="font-size: 24px; font-weight: bold; margin-bottom: 20px; color: #0070ba;">
      ${'{AMOUNT}'}
    </div>
    <div style="margin: 20px 0;">
      <a href="https://www.paypal.com/cgi-bin/webscr?cmd=_xclick&business=<EMAIL>&item_name={PRODUCT_NAME}&amount={AMOUNT}&currency_code=USD&custom={PAYMENT_ID}"
         style="display: inline-block; padding: 12px 24px; background-color: #0070ba; color: white; text-decoration: none; border-radius: 4px; font-weight: bold; font-size: 16px;">
        Pay with PayPal
      </a>
    </div>
    <div style="margin-top: 15px; font-size: 12px; color: #666;">
      Payment ID: {PAYMENT_ID}
    </div>
    <div style="margin-top: 10px;">
      <img src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/pp-acceptance-medium.png" alt="PayPal Acceptance Mark">
    </div>
  </div>
</div>`,
              description: 'Default PayPal button for testing',
              active: true
            }
          ],
          rotationMethod: 'round-robin' as 'round-robin',
          lastUsedIndex: 0
        } as PayPalButtonEmbedConfig
      },
      {
        id: 'trial-custom-link',
        name: 'Trial Custom Payment Links',
        active: true,
        config: {
          links: [
            {
              id: 'trial-link-1',
              name: 'Default Trial Payment Required',
              paymentLink: 'https://example.com/pay-trial',
              buttonText: 'Start Trial',
              successRedirectUrl: 'https://example.com/thank-you-trial',
              active: true
            },
            {
              id: 'trial-link-2',
              name: 'PayPal.me Trial Link',
              paymentLink: 'https://paypal.me/enzidswan/10',
              buttonText: 'Start Trial with PayPal',
              successRedirectUrl: 'https://example.com/trial-started',
              active: true
            },
            {
              id: 'trial-link-3',
              name: 'Stripe Trial Link',
              paymentLink: 'https://buy.stripe.com/test_trial',
              buttonText: 'Start Trial with Stripe',
              successRedirectUrl: '',
              active: true
            }
          ],
          rotationMethod: 'round-robin' as 'round-robin',
          lastUsedIndex: 0
        } as CustomLinkConfig
      },
      {
        id: 'trial-paypal-button-embed',
        name: 'Trial PayPal Button Embed',
        active: true,
        config: {
          buttons: [
            {
              id: 'trial-button-1',
              name: 'Default Trial PayPal Button',
              buttonHtml: `
<div style="text-align: center; margin: 20px 0;">
  <div style="max-width: 500px; margin: 0 auto; border: 1px solid #e0e0e0; border-radius: 5px; padding: 15px; background-color: #f5f8ff;">
    <div style="font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #2c2e2f;">
      Start Trial for {PRODUCT_NAME}
    </div>
    <div style="font-size: 24px; font-weight: bold; margin-bottom: 20px; color: #0070ba;">
      ${'{AMOUNT}'}
    </div>
    <div style="margin: 20px 0;">
      <a href="https://www.paypal.com/cgi-bin/webscr?cmd=_xclick&business=<EMAIL>&item_name=Trial: {PRODUCT_NAME}&amount={AMOUNT}&currency_code=USD&custom={PAYMENT_ID}"
         style="display: inline-block; padding: 12px 24px; background-color: #0070ba; color: white; text-decoration: none; border-radius: 4px; font-weight: bold; font-size: 16px;">
        Start Trial with PayPal
      </a>
    </div>
    <div style="margin-top: 15px; font-size: 12px; color: #666;">
      Payment ID: {PAYMENT_ID}
    </div>
    <div style="margin-top: 10px;">
      <img src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/pp-acceptance-medium.png" alt="PayPal Acceptance Mark">
    </div>
    <div style="margin-top: 10px; padding: 8px; background-color: #e6f7ff; border-radius: 4px; font-size: 12px; color: #0070ba;">
      This is a trial subscription. You can upgrade to a full subscription later.
    </div>
  </div>
</div>`,
              description: 'Default trial PayPal button for testing',
              active: true
            },
            {
              id: 'trial-button-2',
              name: 'Modern Trial PayPal Button',
              buttonHtml: `
<div style="text-align: center; margin: 20px 0;">
  <div style="max-width: 500px; margin: 0 auto; border: 1px solid #e0e0e0; border-radius: 10px; padding: 20px; background-color: #f8faff; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
    <div style="font-size: 20px; font-weight: bold; margin-bottom: 15px; color: #2c2e2f;">
      Start Your Trial Today
    </div>
    <div style="font-size: 16px; margin-bottom: 15px; color: #4a5568;">
      {PRODUCT_NAME} - Limited Time Offer
    </div>
    <div style="font-size: 28px; font-weight: bold; margin-bottom: 20px; color: #0070ba;">
      ${'{AMOUNT}'}
    </div>
    <div style="margin: 20px 0;">
      <a href="https://www.paypal.com/cgi-bin/webscr?cmd=_xclick&business=<EMAIL>&item_name=Trial: {PRODUCT_NAME}&amount={AMOUNT}&currency_code=USD&custom={PAYMENT_ID}"
         style="display: inline-block; padding: 14px 28px; background-color: #0070ba; color: white; text-decoration: none; border-radius: 50px; font-weight: bold; font-size: 16px; transition: all 0.3s ease;">
        Begin Your Trial
      </a>
    </div>
    <div style="margin-top: 15px; font-size: 12px; color: #666;">
      Secure Payment via PayPal - Order ID: {PAYMENT_ID}
    </div>
    <div style="margin-top: 10px;">
      <img src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/pp-acceptance-medium.png" alt="PayPal Acceptance Mark">
    </div>
    <div style="margin-top: 15px; padding: 10px; background-color: #e6f7ff; border-radius: 8px; font-size: 13px; color: #0070ba;">
      <strong>Trial Benefits:</strong> Full access to all features for a limited time. Upgrade anytime.
    </div>
  </div>
</div>`,
              description: 'Modern styled trial PayPal button with rounded corners',
              active: true
            },
            {
              id: 'trial-button-3',
              name: 'Minimalist Trial Button',
              buttonHtml: `
<div style="text-align: center; margin: 20px 0;">
  <div style="max-width: 450px; margin: 0 auto; border: 1px solid #eaeaea; border-radius: 4px; padding: 20px; background-color: #ffffff;">
    <div style="font-size: 18px; font-weight: 500; margin-bottom: 10px; color: #333;">
      Try {PRODUCT_NAME}
    </div>
    <div style="font-size: 22px; font-weight: 600; margin-bottom: 20px; color: #0070ba;">
      ${'{AMOUNT}'} Trial
    </div>
    <div style="margin: 20px 0;">
      <a href="https://www.paypal.com/cgi-bin/webscr?cmd=_xclick&business=<EMAIL>&item_name=Trial: {PRODUCT_NAME}&amount={AMOUNT}&currency_code=USD&custom={PAYMENT_ID}"
         style="display: inline-block; padding: 12px 24px; background-color: #333; color: white; text-decoration: none; border-radius: 3px; font-weight: 500; font-size: 15px;">
        Start Trial
      </a>
    </div>
    <div style="margin-top: 15px; font-size: 12px; color: #777;">
      Secure payment processing by PayPal
    </div>
    <div style="margin-top: 10px;">
      <img src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/pp-acceptance-small.png" alt="PayPal">
    </div>
  </div>
</div>`,
              description: 'Minimalist trial button with clean design',
              active: true
            }
          ],
          rotationMethod: 'round-robin' as 'round-robin',
          lastUsedIndex: 0
        } as PayPalButtonEmbedConfig
      }
    ]
  }
};

/**
 * Get the email configuration from storage
 */
export function getEmailConfig(): EmailConfig {
  return configStorage.email;
}

/**
 * Get the payment configuration from storage
 */
export function getPaymentConfig(): PaymentConfig {
  // Cast the payment config to ensure TypeScript recognizes it as a valid PaymentConfig
  return configStorage.payment as PaymentConfig;
}