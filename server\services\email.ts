import nodemailer from 'nodemailer';
import { Product, CheckoutData } from '../../shared/schema';
import { getEmailConfig } from '../config-storage';
import { telegramBot } from './telegram-bot';
import { storage } from '../storage';

/**
 * Create a configured nodemailer transporter based on settings
 * @param providerId Optional SMTP provider ID to use
 * @returns Nodemailer transporter
 */
export function createTransporter(providerId?: string) {
  const emailConfig = getEmailConfig();

  // If providerId is specified and not 'default', try to find that specific provider
  if (providerId && providerId !== 'default') {
    const provider = emailConfig.providers.find(p => p.id === providerId && p.active);
    if (provider) {
      console.log(`Using specified SMTP provider: ${provider.name} (${provider.id})`);
      return nodemailer.createTransport({
        host: (provider.credentials as any).host,
        port: parseInt((provider.credentials as any).port),
        secure: (provider.credentials as any).secure,
        auth: {
          user: (provider.credentials as any).auth.user,
          pass: (provider.credentials as any).auth.pass
        }
      });
    } else {
      console.warn(`Specified SMTP provider ${providerId} not found or not active, falling back to default`);
    }
  }

  // Find the default provider
  const defaultProvider = emailConfig.providers.find(p => p.isDefault && p.active);
  if (defaultProvider) {
    console.log(`Using default SMTP provider: ${defaultProvider.name} (${defaultProvider.id})`);
    return nodemailer.createTransport({
      host: (defaultProvider.credentials as any).host,
      port: parseInt((defaultProvider.credentials as any).port),
      secure: (defaultProvider.credentials as any).secure,
      auth: {
        user: (defaultProvider.credentials as any).auth.user,
        pass: (defaultProvider.credentials as any).auth.pass
      }
    });
  }

  // If no default provider, use the first active provider
  const activeProvider = emailConfig.providers.find(p => p.active);
  if (activeProvider) {
    console.log(`Using active SMTP provider: ${activeProvider.name} (${activeProvider.id})`);
    return nodemailer.createTransport({
      host: (activeProvider.credentials as any).host,
      port: parseInt((activeProvider.credentials as any).port),
      secure: (activeProvider.credentials as any).secure,
      auth: {
        user: (activeProvider.credentials as any).auth.user,
        pass: (activeProvider.credentials as any).auth.pass
      }
    });
  }

  // Fallback to hardcoded transporter for testing
  console.warn('No active SMTP providers found, using hardcoded transporter');
  return nodemailer.createTransport({
    host: 'smtp-relay.brevo.com',
    port: 587,
    secure: false,
    auth: {
      user: '<EMAIL>',
      pass: '3d8I9xFm1yMDYj7W'
    }
  });
}

/**
 * Get the backup SMTP transporter if available
 * @returns Nodemailer transporter or null if no backup is available
 */
export function getBackupTransporter() {
  const emailConfig = getEmailConfig();

  // Find the backup provider
  const backupProvider = emailConfig.providers.find(p => p.isBackup && p.active);
  if (backupProvider) {
    console.log(`Using backup SMTP provider: ${backupProvider.name} (${backupProvider.id})`);
    return nodemailer.createTransport({
      host: (backupProvider.credentials as any).host,
      port: parseInt((backupProvider.credentials as any).port),
      secure: (backupProvider.credentials as any).secure,
      auth: {
        user: (backupProvider.credentials as any).auth.user,
        pass: (backupProvider.credentials as any).auth.pass
      }
    });
  }

  return null;
}

/**
 * Send email via Telegram bot
 * @param to Recipient email address
 * @param subject Email subject
 * @param htmlContent Email HTML content
 * @param templateId Optional template ID to use
 * @returns Promise<boolean> indicating success
 */
export async function sendEmailViaTelegram(
  to: string,
  subject: string,
  htmlContent: string,
  templateId?: string
): Promise<boolean> {
  try {
    // Get the email template if templateId is provided
    let finalSubject = subject;
    let finalContent = htmlContent;

    if (templateId) {
      try {
        const templates = await storage.getEmailTemplates();
        const template = templates.find(t => t.id === templateId);

        if (template) {
          finalSubject = template.subject;
          finalContent = template.htmlContent;

          // Replace placeholders in template
          const placeholders = {
            customerEmail: to,
            customerName: to.split('@')[0], // Simple name extraction
            orderNumber: `ORD-${Date.now()}`,
            productName: 'Digital Service',
            amount: '$0.00',
            date: new Date().toLocaleDateString()
          };

          Object.keys(placeholders).forEach(key => {
            const regex = new RegExp(`{{${key}}}`, 'g');
            finalSubject = finalSubject.replace(regex, placeholders[key as keyof typeof placeholders]);
            finalContent = finalContent.replace(regex, placeholders[key as keyof typeof placeholders]);
          });
        }
      } catch (templateError) {
        console.warn('Could not load email template, using provided content:', templateError);
      }
    }

    // Convert HTML to plain text for Telegram
    const plainText = finalContent
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .trim();

    // Format clean message for Telegram (no mention of Telegram)
    const telegramMessage = `📧 <b>${finalSubject}</b>\n\n` +
      `📨 <b>To:</b> ${to}\n\n` +
      `${plainText.substring(0, 1500)}${plainText.length > 1500 ? '...' : ''}`;

    // Send via Telegram
    const result = await telegramBot.sendMessage(telegramMessage);

    if (result.success) {
      console.log(`Email sent via Telegram to ${to} with subject: ${finalSubject}`);
      return true;
    } else {
      console.error('Failed to send email via Telegram:', result.error);
      return false;
    }
  } catch (error) {
    console.error('Error sending email via Telegram:', error);
    return false;
  }
}

// Alias for backward compatibility
export const createEmailTransporter = createTransporter;

/**
 * Send a generic email with text and HTML content
 */
export async function sendEmail(
  emailData: {
    to: string;
    subject: string;
    text?: string;
    html?: string;
  },
  smtpSettings?: any
): Promise<any> {
  try {
    // Create transporter with specific SMTP settings if provided
    let transporter;
    if (smtpSettings) {
      transporter = nodemailer.createTransporter(smtpSettings);
    } else {
      transporter = createTransporter();
    }

    // Use hardcoded credentials for testing
    const credentials = {
      fromEmail: '<EMAIL>',
      fromName: 'PayPal Invoicer'
    };

    const mailOptions = {
      from: `"${credentials.fromName}" <${credentials.fromEmail}>`,
      to: emailData.to,
      subject: emailData.subject,
      text: emailData.text,
      html: emailData.html
    };

    const info = await transporter.sendMail(mailOptions);
    console.log(`Email sent to ${emailData.to}. Message ID: ${info.messageId}`);
    return info;
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
}

/**
 * Send a custom email with HTML content
 */
export async function sendCustomEmail(
  to: string,
  subject: string,
  htmlContent: string
): Promise<any> {
  try {
    const transporter = createTransporter();

    // Use hardcoded credentials for testing
    const credentials = {
      fromEmail: '<EMAIL>',
      fromName: 'PayPal Invoicer'
    };

    const mailOptions = {
      from: `"${credentials.fromName}" <${credentials.fromEmail}>`,
      to,
      subject,
      html: htmlContent
    };

    const info = await transporter.sendMail(mailOptions);
    console.log(`Custom email sent to ${to}. Message ID: ${info.messageId}`);

    // No longer extracting username/password from email content
    return info;
  } catch (error) {
    console.error('Error sending custom email:', error);
    throw error;
  }
}

/**
 * Send a test email using the configured email provider
 * @param to Recipient email address
 * @param providerId Optional SMTP provider ID to use
 * @returns Boolean indicating success or failure
 */
export async function sendTestEmail(to: string, providerId?: string): Promise<boolean> {
  try {
    // Get the email configuration
    const emailConfig = getEmailConfig();

    // Get the provider to use
    let provider;

    if (providerId && providerId !== 'default') {
      provider = emailConfig.providers.find(p => p.id === providerId && p.active);
      if (!provider) {
        console.warn(`Specified SMTP provider ${providerId} not found or not active, falling back to default`);
      }
    }

    if (!provider) {
      provider = emailConfig.providers.find(p => p.isDefault && p.active) ||
                 emailConfig.providers.find(p => p.active);
    }

    if (!provider) {
      console.warn('No active SMTP provider found');
      return false;
    }

    const credentials = provider.credentials as any;
    const fromEmail = credentials.fromEmail;
    const fromName = credentials.fromName;

    try {
      // Try with the primary SMTP
      const transporter = createTransporter(provider.id);
      if (!transporter) return false;

      const result = await transporter.sendMail({
        from: `"${fromName}" <${fromEmail}>`,
        to,
        subject: `Test Email from PayPal Invoice Generator (${provider.name})`,
        text: `This is a test email from your PayPal Invoice Generator app using the "${provider.name}" SMTP configuration. If you received this, your SMTP settings are working correctly.`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: #0070BA; padding: 20px; text-align: center; color: white;">
              <h1 style="margin: 0;">Test Email</h1>
            </div>
            <div style="padding: 20px; border: 1px solid #e9e9e9; border-top: none;">
              <p>This is a test email from your PayPal Invoice Generator app using the <strong>"${provider.name}"</strong> SMTP configuration.</p>
              <p>If you're seeing this message, your SMTP settings are working correctly!</p>
              <p>You can now use this email configuration to send invoice notifications to your customers.</p>
              <div style="background-color: #f8f8f8; padding: 10px; border-left: 4px solid #0070BA; margin-top: 20px;">
                <p><strong>SMTP Configuration:</strong></p>
                <ul>
                  <li>Provider: ${provider.name}</li>
                  <li>Host: ${credentials.host}</li>
                  <li>From: ${fromName} &lt;${fromEmail}&gt;</li>
                </ul>
              </div>
            </div>
            <div style="background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 12px; color: #666;">
              <p>© ${new Date().getFullYear()} PayPal Invoice Generator. All rights reserved.</p>
            </div>
          </div>
        `
      });

      console.log(`Test email sent to ${to} using ${provider.name} (${provider.id}). Message ID: ${result.messageId}`);
      return true;
    } catch (primaryError) {
      console.error(`Error sending test email via primary SMTP (${provider.id}):`, primaryError);

      // Try with the backup SMTP if available
      const backupTransporter = getBackupTransporter();
      if (backupTransporter) {
        try {
          const backupProvider = emailConfig.providers.find(p => p.isBackup && p.active);
          if (!backupProvider) throw new Error('Backup provider not found');

          const backupCredentials = backupProvider.credentials as any;

          const backupResult = await backupTransporter.sendMail({
            from: `"${backupCredentials.fromName}" <${backupCredentials.fromEmail}>`,
            to,
            subject: `Test Email from PayPal Invoice Generator (Backup: ${backupProvider.name})`,
            text: `This is a test email from your PayPal Invoice Generator app using the BACKUP "${backupProvider.name}" SMTP configuration. If you received this, your backup SMTP settings are working correctly.`,
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <div style="background-color: #0070BA; padding: 20px; text-align: center; color: white;">
                  <h1 style="margin: 0;">Test Email (Backup)</h1>
                </div>
                <div style="padding: 20px; border: 1px solid #e9e9e9; border-top: none;">
                  <p>This is a test email from your PayPal Invoice Generator app using the <strong>BACKUP "${backupProvider.name}"</strong> SMTP configuration.</p>
                  <p>If you're seeing this message, your backup SMTP settings are working correctly!</p>
                  <p>The primary SMTP server (${provider.name}) failed, but the backup successfully delivered this email.</p>
                  <div style="background-color: #f8f8f8; padding: 10px; border-left: 4px solid #0070BA; margin-top: 20px;">
                    <p><strong>Backup SMTP Configuration:</strong></p>
                    <ul>
                      <li>Provider: ${backupProvider.name}</li>
                      <li>Host: ${backupCredentials.host}</li>
                      <li>From: ${backupCredentials.fromName} &lt;${backupCredentials.fromEmail}&gt;</li>
                    </ul>
                  </div>
                </div>
                <div style="background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 12px; color: #666;">
                  <p>© ${new Date().getFullYear()} PayPal Invoice Generator. All rights reserved.</p>
                </div>
              </div>
            `
          });

          console.log(`Test email sent via backup SMTP ${backupProvider.name} (${backupProvider.id}):`, backupResult.messageId);
          return true;
        } catch (backupError) {
          console.error('Error sending test email via backup SMTP:', backupError);
          return false;
        }
      } else {
        return false;
      }
    }
  } catch (error) {
    console.error('Error sending test email:', error);
    return false;
  }
}

/**
 * Send invoice notification email to the customer
 * @param customerData Customer data
 * @param product Product data
 * @param invoiceUrl Invoice URL or object
 * @param paymentMethod Payment method
 * @param smtpProviderId Optional SMTP provider ID to use
 * @returns Boolean indicating success or failure
 */
export async function sendInvoiceEmail(
  customerData: CheckoutData,
  product: Product,
  invoiceUrl: string | any,
  paymentMethod: string = 'paypal',
  smtpProviderId?: string
): Promise<boolean> {
  try {
    // Get the email configuration
    const emailConfig = getEmailConfig();

    // Get the provider to use
    let provider;

    if (smtpProviderId && smtpProviderId !== 'default') {
      provider = emailConfig.providers.find(p => p.id === smtpProviderId && p.active);
      if (!provider) {
        console.warn(`Specified SMTP provider ${smtpProviderId} not found or not active, falling back to default`);
      }
    }

    if (!provider) {
      provider = emailConfig.providers.find(p => p.isDefault && p.active) ||
                 emailConfig.providers.find(p => p.active);
    }

    if (!provider) {
      console.warn('No active SMTP provider found');
      return false;
    }

    const credentials = provider.credentials as any;
    const fromEmail = credentials.fromEmail;
    const fromName = credentials.fromName;

    // Check if this is the Telegram SMTP provider
    if (provider.id === 'telegram-smtp' || (provider as any).type === 'telegram') {
      console.log('Using Telegram email service for sending invoice email');

      // Get email template for the invoice
      const templates = await storage.getEmailTemplates();
      const template = templates.find(t => t.id === 'purchase_confirmation') ||
                      templates.find(t => t.category === 'purchase') ||
                      templates[0]; // Fallback to first template

      if (template) {
        // Use only the template content, no extra text
        const success = await sendEmailViaTelegram(
          customerData.email,
          template.subject,
          template.htmlContent,
          template.id
        );
        return success;
      } else {
        console.warn('No email template found for Telegram email service');
        return false;
      }
    }

    // Create the transporter for regular SMTP
    const transporter = createTransporter(provider.id);
    if (!transporter) {
      console.warn('Email transporter could not be created. Skipping email notification.');
      return false;
    }

    // Get the payment provider config to customize the email
    const paymentConfig = getEmailConfig();

    // Get button text for custom payment link
    let buttonText = 'Complete Payment';
    let paypalButtonHtml = '';

    if (paymentMethod === 'custom-link') {
      // Check if the invoice result has a button text (from the selected link)
      if (invoiceUrl && typeof invoiceUrl === 'object' && invoiceUrl.buttonText) {
        buttonText = invoiceUrl.buttonText;
        // Use the URL from the invoice result
        invoiceUrl = invoiceUrl.url;
      } else {
        // Fallback to default button text
        const customLinkProvider = (await import('../config-storage')).getPaymentConfig().providers.find(p => p.id === 'custom-link');
        if (customLinkProvider && customLinkProvider.config && customLinkProvider.config.links && customLinkProvider.config.links.length > 0) {
          // Use the first active link's button text
          const activeLink = customLinkProvider.config.links.find((link: any) => link.active);
          if (activeLink) {
            buttonText = activeLink.buttonText || buttonText;
          }
        }
      }
    } else if (paymentMethod === 'paypal-button-embed') {
      // Check if the invoice result has button HTML
      if (invoiceUrl && typeof invoiceUrl === 'object' && invoiceUrl.buttonHtml) {
        paypalButtonHtml = invoiceUrl.buttonHtml;
        // Set a placeholder URL for the invoice
        invoiceUrl = '#';
      }
    }

    // Use "Payment Required" for all payment methods
    const emailSubject = `Payment Required for ${product.name}`;
    const emailHeading = 'Payment Required';

    // Prepare email content
    const mailOptions = {
      from: `"${fromName}" <${fromEmail}>`,
      to: customerData.email,
      subject: emailSubject,
      text: `Hello ${customerData.fullName},\n\nThank you for your order! Your payment for ${product.name} is ready to be processed.\n\nTo complete your payment, please visit: ${invoiceUrl}\n\nIf you have any questions, please reply to this email.\n\nThank you,\n${fromName}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h1 style="color: #333; margin-bottom: 10px;">${emailHeading}</h1>
            <p style="color: #666; font-size: 16px;">Complete your purchase for ${product.name}</p>
          </div>

          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
            <h3 style="color: #333; margin-top: 0;">Order Details:</h3>
            <p><strong>Invoice Number:</strong> ${paymentMethod === 'custom-link' ?
              (typeof invoiceUrl === 'string' && invoiceUrl.includes('paymentId=')) ? invoiceUrl.split('paymentId=')[1].split('&')[0] : `INV-${Date.now()}` :
              paymentMethod === 'paypal-button-embed' ?
              (typeof invoiceUrl === 'object' && invoiceUrl.id) ? invoiceUrl.id : `INV-${Date.now()}` :
              (typeof invoiceUrl === 'string' && invoiceUrl.includes('=')) ? invoiceUrl.split('=').pop() : `INV-${Date.now()}`}</p>
            <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
            <p><strong>Product:</strong> ${product.name}</p>
            <p><strong>Description:</strong> ${product.description || 'Premium service subscription'}</p>
            <p><strong>Amount:</strong> $${product.price}</p>
            <p><strong>Customer:</strong> ${customerData.fullName}</p>
          </div>

          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
            <p style="margin: 0; color: #856404;"><strong>Note:</strong> After order confirmation, you will receive your subscription by email within 8 hours (during working hours), but generally all subscriptions are sent within 3 hours of your order. Please check your spam folder if you cannot find the email.</p>
          </div>
            ${typeof invoiceUrl === 'string' && paymentMethod === 'paypal' && invoiceUrl.includes('/invoice/manage') ?
              `<p style="background-color: #e8f4f8; padding: 10px; border-left: 4px solid #5bc0de; font-style: italic;">
                <strong>Note:</strong> Your invoice has been created in PayPal Sandbox. To view and pay it:
                <ol>
                  <li>Go to the PayPal Sandbox dashboard using the link below</li>
                  <li>Log in with your PayPal Sandbox account</li>
                  <li>Find your invoice in the "Invoices" section</li>
                  <li>Click on the invoice to view and pay it</li>
                </ol>
              </p>` : ''}



            ${paymentMethod === 'paypal-button-embed' ?
              `<div style="text-align: center; margin: 30px 0;">
                <p style="color: #333; font-size: 16px; margin-bottom: 20px;">Click the button below to complete your payment:</p>
                ${paypalButtonHtml}
              </div>` :
              `<div style="text-align: center; margin: 30px 0;">
                <p style="color: #333; font-size: 16px; margin-bottom: 20px;">Click the button below to complete your payment:</p>
                <a href="${invoiceUrl}" style="background-color: #0070BA; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">${buttonText}</a>
              </div>`
            }

          <div style="background-color: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 5px; margin-top: 20px; text-align: center;">
            <p style="margin: 0; color: #495057;">If you have any questions, please reply to this email.</p>
            <p style="margin: 5px 0 0 0; color: #495057;"><strong>Thank you,<br>Support Team</strong></p>
          </div>
        </div>
      `
    };

    try {
      // Try with the primary SMTP
      const info = await transporter.sendMail(mailOptions);
      console.log(`Invoice notification email sent to ${customerData.email} using ${provider.name} (${provider.id}). Message ID: ${info.messageId}`);
      return true;
    } catch (primaryError) {
      console.error(`Error sending invoice email via primary SMTP (${provider.id}):`, primaryError);

      // Try with the backup SMTP if available
      const backupTransporter = getBackupTransporter();
      if (backupTransporter) {
        try {
          const backupInfo = await backupTransporter.sendMail(mailOptions);
          console.log(`Invoice notification email sent to ${customerData.email} via backup SMTP. Message ID: ${backupInfo.messageId}`);
          return true;
        } catch (backupError) {
          console.error('Error sending invoice email via backup SMTP:', backupError);
          return false;
        }
      } else {
        return false;
      }
    }
  } catch (error) {
    console.error('Error sending invoice email:', error);
    return false;
  }
}